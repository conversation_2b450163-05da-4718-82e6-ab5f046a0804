// Email Client JavaScript Functionality

// Global variables
let currentFolder = 'inbox';
let selectedEmails = new Set();
let allEmails = [];
let currentEmail = null;
let searchQuery = '';

// Initialize email client when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeEmailClient();
});

function initializeEmailClient() {
    console.log('Initializing email client...');
    
    // Add event listeners
    setupEventListeners();
    
    // Load initial data
    refreshEmails();
    
    // Setup compose form handler
    setupComposeForm();
      // Setup search functionality
    setupSearch();
    
    console.log('Email client initialized');
}

function setupEventListeners() {
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Click outside modal to close
    const composeModal = document.getElementById('compose-modal');
    if (composeModal) {
        composeModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeComposeModal();
            }
        });
        
        // Prevent event bubbling from compose modal inputs
        composeModal.addEventListener('input', function(e) {
            e.stopPropagation();
        });
        
        composeModal.addEventListener('keydown', function(e) {
            e.stopPropagation();
        });
    }
}

function setupComposeForm() {
    const composeForm = document.getElementById('compose-form');
    if (composeForm) {
        composeForm.onsubmit = function(e) {
            e.preventDefault();
            e.stopPropagation();
            sendEmail();
        };
        
        // Add input event listeners to compose form inputs to prevent interference
        const composeInputs = composeForm.querySelectorAll('input, textarea');
        composeInputs.forEach(input => {
            input.addEventListener('input', function(e) {
                e.stopPropagation(); // Prevent event bubbling
            });
            
            input.addEventListener('keydown', function(e) {
                e.stopPropagation(); // Prevent event bubbling
            });
        });
    }
}

function setupSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            // Only process search if we're not in a modal and this is the search input
            if (!document.getElementById('compose-modal').classList.contains('hidden')) {
                return; // Skip search when compose modal is open
            }
            
            // Double check that this is actually the search input
            if (e.target.id !== 'search-input') {
                return;
            }
            
            searchQuery = e.target.value.toLowerCase();
            console.log('Search triggered with query:', searchQuery);
            filterEmails();
        });
    }
}

function handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + N for new email
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        openComposeModal();
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        closeComposeModal();
    }
    
    // R for reply (when email is selected)
    if (e.key === 'r' && currentEmail) {
        replyToEmail();
    }
    
    // F for forward (when email is selected)
    if (e.key === 'f' && currentEmail) {
        forwardEmail();
    }
    
    // Delete key to delete email
    if (e.key === 'Delete' && currentEmail) {
        deleteEmail();
    }
}

// Navigation functions
function goBack() {
    window.location.href = 'index.html';
}

function switchFolder(folder) {
    currentFolder = folder;
    
    // Update active folder button
    document.querySelectorAll('.folder-btn').forEach(btn => {
        btn.classList.remove('active-folder', 'bg-gray-700', 'text-white');
        btn.classList.add('text-gray-300');
    });
    
    const activeBtn = document.getElementById(folder + '-btn');
    if (activeBtn) {
        activeBtn.classList.add('active-folder', 'bg-gray-700', 'text-white');
        activeBtn.classList.remove('text-gray-300');
    }
    
    // Update folder title
    const folderTitle = document.getElementById('folder-title');
    if (folderTitle) {
        folderTitle.textContent = folder.charAt(0).toUpperCase() + folder.slice(1);
    }
    
    // Clear current selection
    clearEmailSelection();
    
    // Load emails for this folder
    refreshEmails();
}

// Email loading and display functions
function refreshEmails() {
    console.log('refreshEmails called, currentFolder:', currentFolder);
    showLoading();
    
    // For now, we'll only load inbox emails from the API
    if (currentFolder === 'inbox') {
        loadInboxEmails();
    } else {
        // For other folders, show placeholder
        displayPlaceholderEmails();
    }
}

function loadInboxEmails() {
    const base_url = 'http://127.0.0.1:7777/';
    
    axios.get(base_url + 'receive-emails/?limit=50')
        .then(response => {
            const emails = response.data;
            allEmails = emails.map((email, index) => ({
                id: `email-${index}`,
                subject: email.subject || 'No Subject',
                sender: email.sender || 'Unknown Sender',
                body: email.body || 'This email has no content.',
                received_time: email.received_time || new Date().toISOString(),
                read: false,
                folder: 'inbox'
            }));
            
            // Add some test emails if no emails are received
            if (allEmails.length === 0) {
                allEmails = [
                    {
                        id: 'test-1',
                        subject: 'Welcome to Email Client',
                        sender: '<EMAIL>',
                        body: 'Welcome to our email client!\n\nThis is a test email to demonstrate the functionality.\n\nBest regards,\nThe Email Team',
                        received_time: new Date().toISOString(),
                        read: false,
                        folder: 'inbox'
                    },
                    {
                        id: 'test-2',
                        subject: 'System Update Notification',
                        sender: '<EMAIL>',
                        body: 'Dear User,\n\nYour system has been successfully updated to the latest version.\n\nKey improvements:\n- Enhanced security\n- Better performance\n- Bug fixes\n\nThank you for using our service.',
                        received_time: new Date(Date.now() - 3600000).toISOString(),
                        read: false,
                        folder: 'inbox'
                    }
                ];
            }
            
            displayEmails(allEmails);
            updateInboxCount(allEmails.length);
            hideLoading();
        })
        .catch(error => {
            console.error('Error loading emails:', error);
            showToast('Error loading emails: ' + (error.response?.data?.detail || error.message), 'error');
            
            // Show test emails on error
            allEmails = [
                {
                    id: 'test-1',
                    subject: 'Welcome to Email Client',
                    sender: '<EMAIL>',
                    body: 'Welcome to our email client!\n\nThis is a test email to demonstrate the functionality.\n\nBest regards,\nThe Email Team',
                    received_time: new Date().toISOString(),
                    read: false,
                    folder: 'inbox'
                }
            ];
            
            displayEmails(allEmails);
            updateInboxCount(allEmails.length);
            hideLoading();
        });
}

function displayPlaceholderEmails() {
    console.log('=== DISPLAYING PLACEHOLDER EMAILS ===');
    console.log('Displaying placeholder emails for folder:', currentFolder);
    
    if (currentFolder === 'sent') {
        console.log('=== LOADING SENT EMAILS FROM LOCALSTORAGE ===');
        // Load sent emails from localStorage
        try {
            const sentEmailsString = localStorage.getItem('sentEmails');
            console.log('Raw sent emails from localStorage:', sentEmailsString);
            
            const sentEmails = JSON.parse(sentEmailsString || '[]');
            console.log('Parsed sent emails:', sentEmails);
            console.log('Number of sent emails:', sentEmails.length);
            
            if (sentEmails.length > 0) {
                console.log('=== DISPLAYING SENT EMAILS ===');
                allEmails = sentEmails;
                displayEmails(allEmails);
                hideLoading();
                return;
            } else {
                console.log('No sent emails found, showing placeholder');
            }
        } catch (error) {
            console.error('Error loading sent emails:', error);
        }
    }
    
    // Show placeholder for other folders or empty sent folder
    const placeholderEmails = [];
    
    if (currentFolder === 'sent') {
        placeholderEmails.push({
            id: 'placeholder-sent',
            subject: 'No sent emails yet',
            sender: 'System',
            body: 'Emails you send will appear here.',
            received_time: new Date().toISOString(),
            read: true,
            folder: currentFolder
        });
    } else if (currentFolder === 'drafts') {
        placeholderEmails.push({
            id: 'placeholder-drafts',
            subject: 'No drafts saved',
            sender: 'System',
            body: 'Draft emails will appear here when you save them.',
            received_time: new Date().toISOString(),
            read: true,
            folder: currentFolder
        });
    } else if (currentFolder === 'trash') {
        placeholderEmails.push({
            id: 'placeholder-trash',
            subject: 'Trash is empty',
            sender: 'System',
            body: 'Deleted emails will appear here.',
            received_time: new Date().toISOString(),
            read: true,
            folder: currentFolder
        });
    } else {
        placeholderEmails.push({
            id: 'placeholder-1',
            subject: 'No emails in ' + currentFolder,
            sender: 'System',
            body: 'This folder is empty or not yet implemented.',
            received_time: new Date().toISOString(),
            read: true,
            folder: currentFolder
        });
    }
    
    allEmails = placeholderEmails;
    displayEmails(placeholderEmails);
    hideLoading();
}

function displayEmails(emails) {
    const emailList = document.getElementById('email-list');
    
    if (emails.length === 0) {
        displayEmptyState('No emails found');
        return;
    }
    
    emailList.innerHTML = '';
    
    emails.forEach(email => {
        const emailItem = createEmailListItem(email);
        emailList.appendChild(emailItem);
    });
}

function createEmailListItem(email) {
    const emailDiv = document.createElement('div');
    emailDiv.className = `email-item p-4 cursor-pointer transition-all ${email.read ? 'text-gray-400' : 'text-white font-medium'}`;
    emailDiv.setAttribute('data-email-id', email.id);
    
    const formattedDate = formatEmailDate(email.received_time || email.sent_time);
    const truncatedBody = email.body ? email.body.substring(0, 80) + '...' : '';
    
    // For sent emails, show recipient instead of sender
    const displayName = currentFolder === 'sent' && email.to ? email.to : (email.sender || 'Unknown');
    const displayLabel = currentFolder === 'sent' ? 'To:' : '';
    
    emailDiv.innerHTML = `
        <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0 pr-3">
                <div class="flex items-center space-x-2 mb-1">
                    ${displayLabel ? `<span class="text-xs text-gray-500">${displayLabel}</span>` : ''}
                    <span class="text-sm font-semibold truncate ${email.read ? 'text-gray-400' : 'text-white'}">${escapeHtml(displayName)}</span>
                    ${!email.read ? '<span class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></span>' : ''}
                </div>
                <h4 class="text-sm font-medium truncate mb-1 ${email.read ? 'text-gray-500' : 'text-gray-200'}">${escapeHtml(email.subject)}</h4>
                <p class="text-xs text-gray-500 truncate leading-relaxed">${escapeHtml(truncatedBody)}</p>
            </div>
            <div class="text-xs text-gray-500 ml-2 flex-shrink-0 font-medium">${formattedDate}</div>
        </div>
    `;
    
    emailDiv.addEventListener('click', () => selectEmail(email));
    
    return emailDiv;
}

function selectEmail(email) {
    currentEmail = email;
    
    // Update UI selection
    document.querySelectorAll('.email-item').forEach(item => {
        item.classList.remove('selected');
    });
    
    const emailItem = document.querySelector(`[data-email-id="${email.id}"]`);
    if (emailItem) {
        emailItem.classList.add('selected');
    }
    
    // Display email content
    displayEmailContent(email);
    
    // Mark as read
    if (!email.read) {
        email.read = true;
        updateEmailReadStatus(email);
    }
}

function displayEmailContent(email) {
    console.log('Displaying email content:', email);
    
    const emailHeader = document.getElementById('email-header');
    const emailContent = document.getElementById('email-content');
    const welcomeMessage = document.getElementById('welcome-message');
    const emailBodyContent = document.getElementById('email-body-content');
    
    if (!emailHeader || !emailBodyContent || !welcomeMessage) {
        console.error('Required elements not found');
        return;
    }
    
    // Show email header and content
    emailHeader.classList.remove('hidden');
    welcomeMessage.classList.add('hidden');
    emailBodyContent.classList.remove('hidden');
    
    // Update header information
    const subjectEl = document.getElementById('email-subject');
    const senderEl = document.getElementById('email-sender');
    const dateEl = document.getElementById('email-date');
    
    if (subjectEl) subjectEl.textContent = email.subject || 'No Subject';
    
    // For sent emails, show recipient info
    if (senderEl) {
        if (currentFolder === 'sent' && email.to) {
            senderEl.textContent = `To: ${email.to}`;
        } else {
            senderEl.textContent = email.sender || 'Unknown Sender';
        }
    }
    
    if (dateEl) dateEl.textContent = formatEmailDate(email.received_time || email.sent_time);
    
    // Update body content
    const emailBody = document.getElementById('email-body');
    if (emailBody) {
        const formattedBody = formatEmailBody(email.body);
        console.log('Formatted body:', formattedBody);
        emailBody.innerHTML = formattedBody;
    } else {
        console.error('Email body element not found');
    }
}

function formatEmailBody(body) {
    if (!body) return '<p class="text-gray-400 italic">No content available</p>';
    
    // Escape HTML first
    const escapedBody = escapeHtml(body);
    
    // Convert line breaks to paragraphs and handle formatting
    const paragraphs = escapedBody.split('\n\n').filter(p => p.trim());
    
    if (paragraphs.length === 0) {
        // Single paragraph with line breaks
        return `<div class="text-gray-200 leading-relaxed">${escapedBody.replace(/\n/g, '<br>')}</div>`;
    }
    
    return paragraphs.map(p => {
        // Handle different paragraph types
        const trimmedP = p.trim();
        
        // Check if it's a header-like line (short line that might be a title)
        if (trimmedP.length < 50 && !trimmedP.includes('.') && paragraphs.length > 1) {
            return `<h3 class="text-xl font-semibold text-white mb-3 mt-6 first:mt-0">${trimmedP}</h3>`;
        }
        
        // Regular paragraph
        return `<p class="mb-4 text-gray-200 leading-relaxed">${p.replace(/\n/g, '<br>')}</p>`;
    }).join('');
}

function clearEmailSelection() {
    currentEmail = null;
    
    // Hide email content
    document.getElementById('email-header').classList.add('hidden');
    document.getElementById('welcome-message').classList.remove('hidden');
    document.getElementById('email-body-content').classList.add('hidden');
    
    // Clear selection
    document.querySelectorAll('.email-item').forEach(item => {
        item.classList.remove('selected');
    });
}

// Compose email functions
function openComposeModal() {
    console.log('Opening compose modal');
    const modal = document.getElementById('compose-modal');
    modal.classList.remove('hidden');
    
    // Clear form
    document.getElementById('compose-form').reset();
    document.getElementById('compose-status').textContent = '';
    
    // Focus on To field
    setTimeout(() => {
        document.getElementById('compose-to').focus();
    }, 100);
}

function closeComposeModal() {
    console.log('Closing compose modal');
    const modal = document.getElementById('compose-modal');
    modal.classList.add('hidden');
    
    // Clear any status messages
    document.getElementById('compose-status').textContent = '';
}

function sendEmail() {
    console.log('=== SENDING EMAIL ===');
    const to = document.getElementById('compose-to').value.trim();
    const subject = document.getElementById('compose-subject').value.trim();
    const body = document.getElementById('compose-body').value.trim();
    const cc = document.getElementById('compose-cc').value.trim();
    
    if (!to || !subject || !body) {
        showToast('Please fill in all required fields', 'error');
        return;
    }
    
    const emailData = {
        to: to,
        subject: subject,
        body: body,
        cc: cc || undefined
    };      console.log('Email data:', emailData);
    
    // Always add to sent emails first (for offline functionality)
    console.log('=== ADDING TO SENT EMAILS ===');
    const savedEmail = addToSentEmails(emailData);
    console.log('Saved email result:', savedEmail);
    
    showLoading();
    updateComposeStatus('Sending email...', 'info');
    
    const base_url = 'http://127.0.0.1:7777/';
    const options = {
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    axios.post(base_url + 'send-email/', emailData, options)        .then(response => {
            console.log('=== EMAIL SENT SUCCESSFULLY ===');
            console.log('Email sent successfully:', response.data);
            hideLoading();
            
            showToast('Email sent successfully!', 'success');
            closeComposeModal();
            
            // If we're in sent folder, refresh to show the new email
            if (currentFolder === 'sent') {
                console.log('Current folder is sent, refreshing...');
                setTimeout(() => refreshEmails(), 500);
            }
        })        .catch(error => {
            console.log('=== EMAIL SEND FAILED ===');
            console.error('Error sending email:', error);
            hideLoading();
            const errorMessage = error.response?.data?.detail || error.message || 'Unknown error occurred';
            updateComposeStatus('Email saved to sent folder (API error: ' + errorMessage + ')', 'error');
            showToast('Email saved to sent folder (API unavailable)', 'info');
            
            // Close modal after a delay even if API fails
            setTimeout(() => {
                closeComposeModal();
                // If we're in sent folder, refresh to show the new email
                if (currentFolder === 'sent') {
                    console.log('Current folder is sent, refreshing after error...');
                    setTimeout(() => refreshEmails(), 500);
                }
            }, 2000);
        });
}

function addToSentEmails(emailData) {
    console.log('=== ADDING EMAIL TO SENT FOLDER ===');
    console.log('Adding email to sent folder:', emailData);
    try {
        let sentEmails = JSON.parse(localStorage.getItem('sentEmails') || '[]');
        console.log('Current sent emails count:', sentEmails.length);
        console.log('Current sent emails:', sentEmails);
        
        const sentEmail = {
            id: `sent-${Date.now()}`,
            subject: emailData.subject,
            sender: 'You',
            to: emailData.to,
            cc: emailData.cc,
            body: emailData.body,
            sent_time: new Date().toISOString(),
            read: true,
            folder: 'sent'
        };
        sentEmails.unshift(sentEmail); // Add to beginning
        
        // Keep only last 50 sent emails
        if (sentEmails.length > 50) {
            sentEmails = sentEmails.slice(0, 50);
        }
          localStorage.setItem('sentEmails', JSON.stringify(sentEmails));
        console.log('=== EMAIL SAVED TO LOCALSTORAGE ===');
        console.log('Added email to sent folder:', sentEmail);
        console.log('Total sent emails after adding:', sentEmails.length);
        console.log('localStorage sentEmails length:', JSON.parse(localStorage.getItem('sentEmails') || '[]').length);
        
        // Force refresh sent folder if currently viewing it
        if (currentFolder === 'sent') {
            console.log('=== AUTO-REFRESHING SENT FOLDER ===');
            setTimeout(() => {
                console.log('Auto-refreshing sent folder...');
                refreshEmails();
            }, 500);
        }
        
        return sentEmail;
    } catch (error) {
        console.error('Error saving sent email:', error);
        return null;
    }
}

// Test function to add sample sent emails
function addTestSentEmails() {
    console.log('Adding test sent emails...');
    const testEmails = [
        {
            to: '<EMAIL>',
            subject: 'Test Email 1',
            body: 'This is a test email to verify the sent folder functionality.',
            cc: ''
        },
        {
            to: '<EMAIL>',
            subject: 'Test Email 2', 
            body: 'Another test email with more content.\n\nThis email has multiple paragraphs to test the formatting.',
            cc: '<EMAIL>'
        }
    ];
    
    testEmails.forEach(email => addToSentEmails(email));
    showToast('Test sent emails added', 'success');
}

function saveDraft() {
    showToast('Draft saved (feature not implemented)', 'info');
}

function updateComposeStatus(message, type) {
    const statusElement = document.getElementById('compose-status');
    statusElement.textContent = message;
    statusElement.className = `text-sm ${type === 'error' ? 'text-red-400' : type === 'success' ? 'text-green-400' : 'text-blue-400'}`;
}

// Email actions
function replyToEmail() {
    if (!currentEmail) return;
    
    openComposeModal();
    
    // Pre-fill reply data
    document.getElementById('compose-to').value = currentEmail.sender || '';
    document.getElementById('compose-subject').value = 'Re: ' + (currentEmail.subject || '');
    
    const originalBody = currentEmail.body || '';
    const replyBody = `\n\n--- Original Message ---\nFrom: ${currentEmail.sender}\nDate: ${formatEmailDate(currentEmail.received_time)}\nSubject: ${currentEmail.subject}\n\n${originalBody}`;
    document.getElementById('compose-body').value = replyBody;
    
    // Focus on body at the beginning
    const bodyTextarea = document.getElementById('compose-body');
    bodyTextarea.setSelectionRange(0, 0);
    bodyTextarea.focus();
}

function forwardEmail() {
    if (!currentEmail) return;
    
    openComposeModal();
    
    // Pre-fill forward data
    document.getElementById('compose-subject').value = 'Fwd: ' + (currentEmail.subject || '');
    
    const originalBody = currentEmail.body || '';
    const forwardBody = `\n\n--- Forwarded Message ---\nFrom: ${currentEmail.sender}\nDate: ${formatEmailDate(currentEmail.received_time)}\nSubject: ${currentEmail.subject}\n\n${originalBody}`;
    document.getElementById('compose-body').value = forwardBody;
    
    // Focus on To field
    document.getElementById('compose-to').focus();
}

function deleteEmail() {
    if (!currentEmail) return;
    
    if (confirm('Are you sure you want to delete this email?')) {
        // Remove from current view
        const emailItem = document.querySelector(`[data-email-id="${currentEmail.id}"]`);
        if (emailItem) {
            emailItem.remove();
        }
        
        // Clear selection
        clearEmailSelection();
        
        showToast('Email deleted (moved to trash)', 'info');
        
        // Update count
        updateInboxCount(allEmails.filter(e => e.id !== currentEmail.id).length);
    }
}

// Search and filter functions
function filterEmails() {
    console.log('filterEmails called with searchQuery:', searchQuery);
    if (!searchQuery) {
        displayEmails(allEmails);
        return;
    }
    
    const filteredEmails = allEmails.filter(email => {
        return email.subject.toLowerCase().includes(searchQuery) ||
               email.sender.toLowerCase().includes(searchQuery) ||
               email.body.toLowerCase().includes(searchQuery);
    });
    
    displayEmails(filteredEmails);
}

function toggleSelectAll() {
    const emailItems = document.querySelectorAll('.email-item');
    const allSelected = selectedEmails.size === emailItems.length;
    
    if (allSelected) {
        selectedEmails.clear();
        emailItems.forEach(item => item.classList.remove('selected'));
    } else {
        selectedEmails.clear();
        emailItems.forEach(item => {
            const emailId = item.getAttribute('data-email-id');
            selectedEmails.add(emailId);
            item.classList.add('selected');
        });
    }
}

// Utility functions
function showLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.classList.remove('hidden');
    }
}

function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.classList.add('hidden');
    }
}

function showToast(message, type = 'info') {
    console.log('Showing toast:', message, type);
    const container = document.getElementById('toast-container');
    if (!container) {
        console.error('Toast container not found');
        return;
    }
    
    const toast = document.createElement('div');
    toast.className = `max-w-sm bg-gray-800 border border-gray-600 rounded-lg shadow-xl pointer-events-auto transform transition-all duration-300 ease-in-out translate-x-0`;
    
    const iconClass = type === 'success' ? 'fa-check-circle text-green-400' : 
                     type === 'error' ? 'fa-exclamation-triangle text-red-400' : 
                     'fa-info-circle text-blue-400';
    
    const bgColor = type === 'success' ? 'border-green-500' : 
                   type === 'error' ? 'border-red-500' : 
                   'border-blue-500';
    
    toast.className += ` ${bgColor}`;
    
    toast.innerHTML = `
        <div class="flex p-4">
            <div class="flex-shrink-0">
                <i class="fas ${iconClass} text-lg"></i>
            </div>
            <div class="ml-3 w-0 flex-1">
                <p class="text-sm font-medium text-white leading-5">${escapeHtml(message)}</p>
            </div>
            <div class="ml-4 flex-shrink-0 flex">
                <button class="inline-flex text-gray-400 hover:text-gray-200 focus:outline-none transition-colors" onclick="this.closest('.max-w-sm').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
        toast.style.opacity = '1';
    }, 10);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }
    }, 5000);
}

function displayEmptyState(message) {
    const emailList = document.getElementById('email-list');
    emailList.innerHTML = `
        <div class="p-8 text-center text-gray-400">
            <i class="fas fa-inbox text-4xl mb-4"></i>
            <p class="text-lg font-medium mb-2">No emails found</p>
            <p class="text-sm">${escapeHtml(message)}</p>
        </div>
    `;
}

function updateInboxCount(count) {
    const inboxCount = document.getElementById('inbox-count');
    if (inboxCount) {
        inboxCount.textContent = count;
    }
}

function updateEmailReadStatus(email) {
    const emailItem = document.querySelector(`[data-email-id="${email.id}"]`);
    if (emailItem) {
        emailItem.classList.remove('font-medium');
        emailItem.classList.add('text-gray-400');
        
        // Remove unread indicator
        const unreadIndicator = emailItem.querySelector('.bg-blue-500');
        if (unreadIndicator) {
            unreadIndicator.remove();
        }
    }
}

function formatEmailDate(dateString) {
    if (!dateString) return '';
    
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) {
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diffDays === 1) {
            return 'Yesterday';
        } else if (diffDays < 7) {
            return date.toLocaleDateString([], { weekday: 'short' });
        } else {
            return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    } catch (error) {
        return '';
    }
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Export functions for global access
window.goBack = goBack;
window.switchFolder = switchFolder;
window.refreshEmails = refreshEmails;
window.openComposeModal = openComposeModal;
window.closeComposeModal = closeComposeModal;
window.sendEmail = sendEmail;
window.saveDraft = saveDraft;
window.replyToEmail = replyToEmail;
window.forwardEmail = forwardEmail;
window.deleteEmail = deleteEmail;
window.toggleSelectAll = toggleSelectAll;
window.addTestSentEmails = addTestSentEmails;
window.openCockpit = function() { window.location.href = 'cockpit.html'; };
