from typing import Optional
from pydantic import BaseModel

class PathModel(BaseModel):
    path: str
    message: Optional[str]

class HelloModel(BaseModel):
    name: str
    message: Optional[str]

class EmailSendModel(BaseModel):
    to: str
    subject: str
    body: str
    cc: Optional[str] = None
    attachments: Optional[list[str]] = None

class EmailReceiveModel(BaseModel):
    subject: Optional[str]
    sender: Optional[str]
    body: Optional[str]
    received_time: Optional[str]
