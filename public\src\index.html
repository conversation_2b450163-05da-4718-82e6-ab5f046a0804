
<!DOCTYPE html>
<html class="dark">

<head>
  <meta charset="UTF-8">
  <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
  <title>SC Operator KI</title>

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Preline UI -->
  <link rel="stylesheet" href="https://preline.co/assets/css/main.min.css">
  <script src="https://preline.co/assets/js/hs-ui.bundle.js"></script>

  <style>
    /* Background SVG styling */
    body {
      background-image: url('../../GiRa_background.svg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-attachment: fixed;
      min-height: 100vh;
    }

    /* Floating dock animations and glass morphism */
    #floating-dock {
      animation: slideUpFade 0.5s ease-out;
    }

    @keyframes slideUpFade {
      from {
        opacity: 0;
        transform: translate(-50%, 20px);
      }
      to {
        opacity: 1;
        transform: translate(-50%, 0);
      }
    }

    #floating-dock button {
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    #floating-dock button::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: all 0.3s ease;
    }

    #floating-dock button:hover::before {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }

    /* Glass morphism effect for dock container */
    .glass-morphism {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    /* Dock icon styling */
    .dock-icon {
      width: 20px;
      height: 20px;
      stroke-width: 1.5;
      transition: all 0.3s ease;
    }

    #floating-dock button:hover .dock-icon {
      transform: scale(1.1);
      stroke-width: 2;
    }

    /* Navbar glassmorphism styles */
    header.glass-panel {
      background: rgba(31, 41, 55, 0.2);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      position: sticky;
      top: 0;
      z-index: 40;
    }

    header.glass-panel:hover {
      background: rgba(31, 41, 55, 0.25);
      border-color: rgba(255, 255, 255, 0.08);
      transform: none; /* Prevent navbar movement on hover */
      box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
    }

    /* Navbar button glassmorphism */
    header .glass-card {
      background: rgba(55, 65, 81, 0.2);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      transition: all 0.3s ease;
    }

    header .glass-card:hover {
      background: rgba(75, 85, 99, 0.3);
      border-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* Logo container styling */
    .logo-container {
      background: rgba(55, 65, 81, 0.15);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.03);
      transition: all 0.3s ease;
    }

    .logo-container:hover {
      background: rgba(75, 85, 99, 0.25);
      border-color: rgba(255, 255, 255, 0.08);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .logo-container img {
      filter: brightness(0.9) contrast(1.1);
      transition: all 0.3s ease;
    }

    .logo-container:hover img {
      filter: brightness(1) contrast(1.2);
      transform: scale(1.05);
    }

    /* AI Assistant Brain Sphere Animation */
    .ai-brain-sphere {
      position: relative;
      width: 100px;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
    }

    .brain-container {
      position: relative;
      width: 90px;
      height: 90px;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #7c3aed, #6d28d9, #4c1d95);
      overflow: hidden;
      box-shadow:
        0 0 25px rgba(139, 92, 246, 0.4),
        inset 0 0 25px rgba(168, 85, 247, 0.2);
    }

    .brain-mesh {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        /* Horizontal flowing lines */
        linear-gradient(90deg, transparent 0%, rgba(168, 85, 247, 0.4) 20%, transparent 40%, rgba(192, 132, 252, 0.3) 60%, transparent 80%, rgba(139, 92, 246, 0.5) 100%),
        /* Vertical flowing lines */
        linear-gradient(0deg, transparent 0%, rgba(192, 132, 252, 0.3) 25%, transparent 50%, rgba(168, 85, 247, 0.4) 75%, transparent 100%),
        /* Diagonal mesh pattern */
        linear-gradient(45deg, transparent 30%, rgba(139, 92, 246, 0.2) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(192, 132, 252, 0.2) 50%, transparent 70%);
      background-size:
        100% 20px,
        20px 100%,
        40px 40px,
        40px 40px;
      border-radius: 50%;
      animation: brainFlow 4s ease-in-out infinite;
    }

    .brain-waves {
      position: absolute;
      top: 10%;
      left: 10%;
      right: 10%;
      bottom: 10%;
      background:
        radial-gradient(ellipse at 20% 30%, rgba(168, 85, 247, 0.6) 0%, transparent 50%),
        radial-gradient(ellipse at 80% 70%, rgba(192, 132, 252, 0.4) 0%, transparent 50%),
        radial-gradient(ellipse at 50% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 60%),
        radial-gradient(ellipse at 30% 80%, rgba(168, 85, 247, 0.5) 0%, transparent 40%);
      border-radius: 50%;
      animation: waveFlow 6s ease-in-out infinite reverse;
    }

    .brain-core {
      position: absolute;
      top: 25%;
      left: 25%;
      right: 25%;
      bottom: 25%;
      background: radial-gradient(circle, rgba(192, 132, 252, 0.8) 0%, rgba(139, 92, 246, 0.4) 50%, transparent 100%);
      border-radius: 50%;
      animation: coreGlow 3s ease-in-out infinite;
    }

    .brain-particles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 50%;
    }

    .particle {
      position: absolute;
      width: 2px;
      height: 2px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      box-shadow: 0 0 4px rgba(168, 85, 247, 0.8);
      animation: particleFloat 3s ease-in-out infinite;
    }

    .particle:nth-child(1) { top: 25%; left: 35%; animation-delay: 0s; }
    .particle:nth-child(2) { top: 35%; right: 30%; animation-delay: 0.5s; }
    .particle:nth-child(3) { bottom: 35%; left: 30%; animation-delay: 1s; }
    .particle:nth-child(4) { bottom: 30%; right: 35%; animation-delay: 1.5s; }
    .particle:nth-child(5) { top: 45%; left: 55%; animation-delay: 2s; }
    .particle:nth-child(6) { top: 55%; right: 45%; animation-delay: 2.5s; }

    @keyframes brainFlow {
      0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 0.8;
      }
      25% {
        transform: rotate(90deg) scale(1.05);
        opacity: 0.9;
      }
      50% {
        transform: rotate(180deg) scale(0.95);
        opacity: 1;
      }
      75% {
        transform: rotate(270deg) scale(1.02);
        opacity: 0.9;
      }
    }

    @keyframes waveFlow {
      0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.6;
      }
      33% {
        transform: scale(1.1) rotate(120deg);
        opacity: 0.8;
      }
      66% {
        transform: scale(0.9) rotate(240deg);
        opacity: 0.7;
      }
    }

    @keyframes coreGlow {
      0%, 100% {
        transform: scale(1);
        opacity: 0.6;
      }
      50% {
        transform: scale(1.2);
        opacity: 0.9;
      }
    }

    @keyframes particleFloat {
      0%, 100% {
        opacity: 0.4;
        transform: translateY(0px) scale(0.8);
      }
      50% {
        opacity: 1;
        transform: translateY(-10px) scale(1.2);
      }
    }

    /* Modal Animations */
    .modal-enter {
      opacity: 1;
    }

    .modal-enter #modalContent {
      transform: scale(1);
      opacity: 1;
    }

    .modal-exit {
      opacity: 0;
    }

    .modal-exit #modalContent {
      transform: scale(0.95);
      opacity: 0;
    }

    /* Custom Range Slider */
    .slider::-webkit-slider-thumb {
      appearance: none;
      height: 20px;
      width: 20px;
      border-radius: 50%;
      background: linear-gradient(45deg, #8b5cf6, #6366f1);
      cursor: pointer;
      box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
    }

    .slider::-moz-range-thumb {
      height: 20px;
      width: 20px;
      border-radius: 50%;
      background: linear-gradient(45deg, #8b5cf6, #6366f1);
      cursor: pointer;
      border: none;
      box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
    }

    /* Vertical Stepper Styles */
    .stepper-container {
      position: relative;
    }

    .step {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24px;
      position: relative;
    }

    .step:last-child {
      margin-bottom: 0;
    }

    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14px;
      margin-right: 16px;
      position: relative;
      z-index: 2;
      transition: all 0.3s ease;
    }

    .step-number.active {
      background: linear-gradient(135deg, #8b5cf6, #6366f1);
      color: white;
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
    }

    .step-number.completed {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }

    .step-number.upcoming {
      background: rgba(107, 114, 128, 0.2);
      color: #9ca3af;
      border: 2px solid #374151;
    }

    .step-content {
      flex: 1;
      min-height: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .step-title {
      font-weight: 600;
      color: #f9fafb;
      margin-bottom: 4px;
    }

    .step-description {
      color: #9ca3af;
      font-size: 14px;
    }

    .step-line {
      position: absolute;
      left: 19px;
      top: 50px;
      width: 2px;
      height: calc(100% - 50px);
      background: #374151;
      z-index: 1;
    }

    .step-line.active {
      background: linear-gradient(180deg, #8b5cf6, #6366f1);
    }

    .step-line.completed {
      background: linear-gradient(180deg, #10b981, #059669);
    }

    .step:last-child .step-line {
      display: none;
    }

    .step-form {
      margin-top: 16px;
      padding: 16px;
      background: rgba(55, 65, 81, 0.3);
      border-radius: 8px;
      border: 1px solid rgba(75, 85, 99, 0.5);
    }

    .step-form.hidden {
      display: none;
    }


  </style>

</head>

<body class="bg-gray-900 text-white">
  <!-- Header -->
  <header class="glass-panel border-b border-gray-700/50 px-6 py-4 backdrop-blur-xl">
    <div class="flex items-center justify-between">
      <!-- Left Section - Title -->
      <div class="flex items-center space-x-4">
        <div>
          <h1 class="text-2xl font-bold text-white">SC Operator KI</h1>
        </div>
      </div>
      
      <!-- Center Section - Mercedes Logo -->
      <div class="absolute left-1/2 transform -translate-x-1/2">
        <div class="">
          <img src="../assets/media/logo/mb_star.png" alt="Mercedes-Benz" class="h-10 w-10 opacity-90 hover:opacity-100 transition-all duration-300 hover:scale-110">
        </div>
      </div>
      
      <!-- Right Section - Company Logos -->
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-3">
          <!-- MO PSCA Logo -->
          <div class="">
            <img src="../assets/media/logo/mo_psca.png" alt="MO PSCA" class="h-8 w-auto opacity-90 hover:opacity-100 transition-opacity duration-200" onerror="console.log('Failed to load MO PSCA logo:', this.src, window.location.href); this.style.display='none';">
          </div>
          <!-- MO HUB Logo -->
          <div class="">
            <img src="../assets/media/logo/mo_hub.png" alt="MO HUB" class="h-8 w-auto opacity-90 hover:opacity-100 transition-opacity duration-200" onerror="console.log('Failed to load MO HUB logo:', this.src, window.location.href); this.style.display='none';">
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="max-w-4xl mx-auto p-6">
    <div class="space-y-8">
      <!-- AI Assistant Section -->
      <div class="space-y-6">
        <!-- Name Input Section -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
          <h3 class="text-lg font-semibold text-white mb-4 text-center">AI Assistant</h3>
          
          <!-- AI Brain Sphere -->
          <div class="ai-brain-sphere">
            <div class="brain-container">
              <div class="brain-mesh"></div>
              <div class="brain-waves"></div>
              <div class="brain-core"></div>
              <div class="brain-particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
              </div>
            </div>
          </div>
          
          <!-- Start AI Process Button -->
          <div class="flex justify-center mb-6">
            <button id="startAiButton" class="group relative inline-flex h-12 w-12 items-center justify-center overflow-hidden rounded-full font-medium text-white transition-all duration-300 hover:w-32 shadow-lg hover:shadow-teal-500/25 border-2 backdrop-blur-sm" style="background: linear-gradient(rgba(255,255,255,0.1), rgba(255,255,255,0.05)) padding-box, linear-gradient(45deg, #14b8a6, #06b6d4, #8b5cf6, #14b8a6) border-box; border: 2px solid transparent;">
              <div class="inline-flex whitespace-nowrap opacity-0 transition-all duration-200 group-hover:-translate-x-3 group-hover:opacity-100">Start AI</div>
              <div class="absolute right-3.5">
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5">
                  <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                </svg>
              </div>
            </button>
          </div>
          
        </div>

      </div>
    </div>
  </div>

  <!-- AI Configuration Modal -->
  <div id="aiModal" class="fixed inset-0 z-50 hidden">
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300"></div>
    
    <!-- Modal Container -->
    <div class="fixed inset-0 flex items-center justify-center p-4">
      <div class="bg-gray-800/90 backdrop-blur-lg border border-gray-700/50 rounded-2xl shadow-2xl max-w-md w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700/50">
          <div class="flex items-center space-x-3">
            <!-- Small AI Brain Sphere for Modal -->
            <div class="w-8 h-8 relative">
              <div class="w-full h-full rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 shadow-lg shadow-purple-500/25 animate-pulse"></div>
            </div>
            <h3 class="text-xl font-semibold text-white">SC Copilot KI</h3>
          </div>
          <button id="closeModal" class="text-gray-400 hover:text-white transition-colors p-1 rounded-lg hover:bg-gray-700/50">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
          <div class="stepper-container">
            <!-- Step 1: AI Model Selection -->
            <div class="step" id="step1">
              <div class="step-number active">1</div>
              <div class="step-content">
                <div class="step-title">Select AI Model</div>
                <div class="step-description">Choose the AI model for processing</div>
                <div class="step-form" id="form1">
                  <label class="block text-sm font-medium text-gray-300 mb-2">AI Model</label>
                  <select id="aiModel" class="w-full px-4 py-2 bg-gray-700/70 border border-gray-600 rounded-lg text-white focus:border-purple-500 focus:ring-1 focus:ring-purple-500">
                    <option value="gpt-4">GPT-4 (Most Capable)</option>
                    <option value="gpt-3.5">GPT-3.5 Turbo (Fast)</option>
                    <option value="claude">Claude (Analytical)</option>
                    <option value="local">Local Model (Private)</option>
                  </select>
                </div>
              </div>
              <div class="step-line active"></div>
            </div>

            <!-- Step 2: Processing Mode -->
            <div class="step" id="step2">
              <div class="step-number upcoming">2</div>
              <div class="step-content">
                <div class="step-title">Processing Mode</div>
                <div class="step-description">Configure how the AI should process requests</div>
                <div class="step-form hidden" id="form2">
                  <label class="block text-sm font-medium text-gray-300 mb-3">Processing Mode</label>
                  <div class="space-y-3">
                    <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-700/30 transition-colors">
                      <input type="radio" name="mode" value="standard" checked class="text-purple-500 focus:ring-purple-500 focus:ring-2">
                      <div>
                        <div class="text-gray-300 font-medium">Standard Processing</div>
                        <div class="text-gray-400 text-sm">Balanced speed and accuracy</div>
                      </div>
                    </label>
                    <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-700/30 transition-colors">
                      <input type="radio" name="mode" value="enhanced" class="text-purple-500 focus:ring-purple-500 focus:ring-2">
                      <div>
                        <div class="text-gray-300 font-medium">Enhanced Analysis</div>
                        <div class="text-gray-400 text-sm">Deep analysis with detailed insights</div>
                      </div>
                    </label>
                    <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-700/30 transition-colors">
                      <input type="radio" name="mode" value="creative" class="text-purple-500 focus:ring-purple-500 focus:ring-2">
                      <div>
                        <div class="text-gray-300 font-medium">Creative Mode</div>
                        <div class="text-gray-400 text-sm">Innovative and creative responses</div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
              <div class="step-line"></div>
            </div>

            <!-- Step 3: Priority & Settings -->
            <div class="step" id="step3">
              <div class="step-number upcoming">3</div>
              <div class="step-content">
                <div class="step-title">Priority & Settings</div>
                <div class="step-description">Set task priority and additional options</div>
                <div class="step-form hidden" id="form3">
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-300 mb-2">Task Priority</label>
                      <input type="range" min="1" max="10" value="5" id="prioritySlider" class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider">
                      <div class="flex justify-between text-xs text-gray-400 mt-1">
                        <span>Low (1)</span>
                        <span id="priorityValue">Medium (5)</span>
                        <span>High (10)</span>
                      </div>
                    </div>
                    <div class="flex items-center space-x-3">
                      <input type="checkbox" id="notifications" class="rounded text-purple-500 focus:ring-purple-500 focus:ring-2">
                      <label for="notifications" class="text-gray-300">Enable notifications</label>
                    </div>
                    <div class="flex items-center space-x-3">
                      <input type="checkbox" id="saveConfig" checked class="rounded text-purple-500 focus:ring-purple-500 focus:ring-2">
                      <label for="saveConfig" class="text-gray-300">Save configuration</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex space-x-3 p-6 border-t border-gray-700/50">
          <button id="cancelModal" class="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
            Cancel
          </button>
          <button id="prevStep" class="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors hidden">
            Previous
          </button>
          <button id="nextStep" class="px-6 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-purple-500/25">
            Next
          </button>
          <button id="startAiProcess" class="px-6 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-green-500/25 hidden">
            Start Process
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Floating Dock -->
  <div id="floating-dock" class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
    <div class="glass-morphism rounded-xl px-3 py-2 shadow-2xl">
      <div class="flex items-center space-x-3">
        <!-- Home/Main Page -->
        <button onclick="goToMainPage()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-blue-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Main Page">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
          </svg>
        </button>
        
        <!-- Cockpit/Dashboard -->
        <button onclick="openCockpit()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-purple-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Cockpit Dashboard">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />
          </svg>
        </button>

        <!-- Email Client -->
        <button onclick="openEmailClient()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-gray-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Email Client">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <script src="../assets/js/python.js"></script>
  <script src="../assets/js/renderer.js"></script>
  <!-- Removed hero-ui-entry.js script as part of React removal -->

  <script>
    // Modal functionality
    const aiModal = document.getElementById('aiModal');
    const modalContent = document.getElementById('modalContent');
    const startAiButton = document.getElementById('startAiButton');
    const closeModal = document.getElementById('closeModal');
    const cancelModal = document.getElementById('cancelModal');
    const startAiProcess = document.getElementById('startAiProcess');
    const nextStep = document.getElementById('nextStep');
    const prevStep = document.getElementById('prevStep');
    const prioritySlider = document.getElementById('prioritySlider');
    const priorityValue = document.getElementById('priorityValue');

    let currentStep = 1;
    const totalSteps = 3;

    // Update priority value display
    prioritySlider.addEventListener('input', (e) => {
      const value = e.target.value;
      const labels = ['Very Low', 'Low', 'Low-Medium', 'Medium-Low', 'Medium', 'Medium-High', 'High-Medium', 'High', 'Very High', 'Critical'];
      priorityValue.textContent = `${labels[value - 1]} (${value})`;
    });

    // Open modal
    function openAiModal() {
      aiModal.classList.remove('hidden');
      aiModal.classList.add('modal-enter');
      aiModal.classList.remove('modal-exit');
      
      // Reset to first step
      currentStep = 1;
      updateStepperUI();
      
      // Trigger animation
      setTimeout(() => {
        modalContent.style.transform = 'scale(1)';
        modalContent.style.opacity = '1';
      }, 10);
    }

    // Close modal
    function closeAiModal() {
      aiModal.classList.add('modal-exit');
      aiModal.classList.remove('modal-enter');
      
      setTimeout(() => {
        aiModal.classList.add('hidden');
        aiModal.classList.remove('modal-exit');
        modalContent.style.transform = 'scale(0.95)';
        modalContent.style.opacity = '0';
        // Reset to first step when closing
        currentStep = 1;
        updateStepperUI();
      }, 300);
    }

    // Update stepper UI based on current step
    function updateStepperUI() {
      // Update step numbers and lines
      for (let i = 1; i <= totalSteps; i++) {
        const stepNumber = document.querySelector(`#step${i} .step-number`);
        const stepLine = document.querySelector(`#step${i} .step-line`);
        const stepForm = document.getElementById(`form${i}`);
        
        // Reset classes
        stepNumber.className = 'step-number';
        if (stepLine) stepLine.className = 'step-line';
        
        if (i < currentStep) {
          // Completed step
          stepNumber.classList.add('completed');
          stepNumber.innerHTML = '✓';
          if (stepLine) stepLine.classList.add('completed');
        } else if (i === currentStep) {
          // Current step
          stepNumber.classList.add('active');
          stepNumber.textContent = i;
          if (stepLine) stepLine.classList.add('active');
        } else {
          // Upcoming step
          stepNumber.classList.add('upcoming');
          stepNumber.textContent = i;
        }
        
        // Show/hide forms
        if (stepForm) {
          if (i === currentStep) {
            stepForm.classList.remove('hidden');
          } else {
            stepForm.classList.add('hidden');
          }
        }
      }
      
      // Update buttons
      prevStep.classList.toggle('hidden', currentStep === 1);
      nextStep.classList.toggle('hidden', currentStep === totalSteps);
      startAiProcess.classList.toggle('hidden', currentStep !== totalSteps);
      
      // Update button text
      if (currentStep === totalSteps) {
        nextStep.textContent = 'Finish';
      } else {
        nextStep.textContent = 'Next';
      }
    }

    // Next step
    function goToNextStep() {
      if (currentStep < totalSteps) {
        currentStep++;
        updateStepperUI();
      }
    }

    // Previous step
    function goToPrevStep() {
      if (currentStep > 1) {
        currentStep--;
        updateStepperUI();
      }
    }

    // Event listeners
    startAiButton.addEventListener('click', openAiModal);
    closeModal.addEventListener('click', closeAiModal);
    cancelModal.addEventListener('click', closeAiModal);
    nextStep.addEventListener('click', goToNextStep);
    prevStep.addEventListener('click', goToPrevStep);

    // Close modal when clicking backdrop
    aiModal.addEventListener('click', (e) => {
      if (e.target === aiModal) {
        closeAiModal();
      }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !aiModal.classList.contains('hidden')) {
        closeAiModal();
      }
    });

    // Start AI Process button
    startAiProcess.addEventListener('click', () => {
      // Get form values
      const model = document.getElementById('aiModel').value;
      const mode = document.querySelector('input[name="mode"]:checked').value;
      const priority = document.getElementById('prioritySlider').value;
      const notifications = document.getElementById('notifications').checked;
      const saveConfig = document.getElementById('saveConfig').checked;
      
      // Log configuration
      console.log('Starting AI Process with:', {
        model,
        mode,
        priority,
        notifications,
        saveConfig
      });
      
      // Close modal
      closeAiModal();
      
      // Show success message
      const priorityLabels = ['Very Low', 'Low', 'Low-Medium', 'Medium-Low', 'Medium', 'Medium-High', 'High-Medium', 'High', 'Very High', 'Critical'];
      alert(`✅ AI Process started successfully!
      
Model: ${model.toUpperCase()}
Mode: ${mode.charAt(0).toUpperCase() + mode.slice(1)}
Priority: ${priorityLabels[priority - 1]} (${priority})
Notifications: ${notifications ? 'Enabled' : 'Disabled'}
Config Saved: ${saveConfig ? 'Yes' : 'No'}`);
    });

    // Initialize stepper UI
    updateStepperUI();
  </script>
</body>

</html>
